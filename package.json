{"name": "vitamin", "license": "MIT", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "vite build", "commit": "cz", "dev": "vite --open --force", "prepare": "husky install", "preview": "vite preview", "preview:test": "start-server-and-test preview http://localhost:4173", "format": "prettier . --write", "run-tsc": "tsc", "run-stylelint": "stylelint --cache --fix --ignore-path .gitignore **/*.css", "validate": "run-p lint test:ci test:e2e:headless", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@douyinfe/semi-illustrations": "^2.52.0", "@douyinfe/semi-ui": "^2.39.2", "@emotion/styled": "^11.11.0", "@locator/babel-jsx": "^0.4.3", "@react-pdf/renderer": "^4.3.0", "@reactivers/hooks": "^1.0.15", "@semi-bot/semi-theme-vrdemo": "^1.0.0", "@semi-bot/semi-theme-vren": "^1.0.0", "@sentry/react": "^8.32.0", "@tanstack/react-query": "4.32.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@turf/turf": "^6.5.0", "@uiw/react-baidu-map": "^2.7.1", "ahooks": "^3.7.8", "cesium": "^1.111.0", "chinese-calendar": "^1.0.3", "chinese-workday": "^1.9.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "daisyui": "3.9.4", "dayjs": "^1.11.9", "docx": "^8.2.3", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^11.0.13", "html2pdf.js": "^0.10.1", "immer": "^10.1.1", "jotai": "^2.3.1", "jotai-devtools": "^0.10.1", "katex": "^0.16.9", "lodash-es": "^4.17.21", "mediainfo.js": "^0.3.1", "mockjs": "^1.1.0", "numeral": "^2.0.6", "plyr-react": "^5.3.0", "qrcode": "^1.5.3", "ramda": "^0.29.0", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-bmapgl": "^0.2.26", "react-color": "^2.19.3", "react-dom": "18.2.0", "react-grid-layout": "^1.4.4", "react-katex": "^3.0.1", "react-query-devtools": "^2.6.3", "react-resize-detector": "^10.0.1", "react-router-dom": "6.14.2", "react-slick": "^0.30.2", "react-sortablejs": "^6.1.4", "remixicon": "^3.5.0", "slick-carousel": "^1.8.1", "smooth-signature": "^1.0.15", "sortablejs": "^1.15.0", "tdesign-icons-react": "^0.3.2", "tdesign-react": "^1.5.2", "typescript-formatter": "^7.2.2", "ulid": "^2.3.0", "use-immer": "^0.10.0", "usehooks-ts": "^2.12.1", "uuid": "^9.0.0", "video.js": "^8.12.0", "vite-plugin-cesium": "^1.2.22"}, "devDependencies": {"@douyinfe/semi-icons": "^2.77.1", "@douyinfe/semi-webpack-plugin": "^2.60.0", "@nabla/vite-plugin-eslint": "1.5.0", "@tailwindcss/forms": "0.5.4", "@testing-library/cypress": "9.0.0", "@testing-library/dom": "9.3.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bmapgl": "^0.0.7", "@types/css-mediaquery": "0.1.1", "@types/file-saver": "^2.0.7", "@types/numeral": "^2.0.5", "@types/ramda": "^0.29.6", "@types/react": "18.2.15", "@types/react-beautiful-dnd": "^13.1.7", "@types/react-dom": "18.2.7", "@types/react-router-dom": "5.3.3", "@types/react-slick": "^0.23.13", "@types/sortablejs": "^1.15.7", "@types/testing-library__jest-dom": "^6.0.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "4.0.3", "@vitest/coverage-istanbul": "0.33.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "10.4.14", "canvas": "^3.1.2", "commitizen": "4.3.0", "css-mediaquery": "0.1.2", "cz-conventional-changelog": "3.3.0", "eslint": "8.45.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "17.1.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-cypress": "2.13.3", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.33.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-prefer-function-component": "3.1.0", "eslint-plugin-testing-library": "5.11.0", "eslint-plugin-unicorn": "48.0.0", "husky": "8.0.3", "jsdom": "^26.1.0", "less": "^4.2.0", "lint-staged": "13.2.3", "msw": "^2.10.4", "npm-run-all": "4.1.5", "postcss": "8.4.27", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "start-server-and-test": "2.0.0", "stylelint": "15.10.2", "stylelint-config-prettier": "9.0.5", "stylelint-config-standard": "33.0.0", "tailwindcss": "3.3.3", "typescript": "5.1.6", "vite": "4.4.6", "vite-plugin-mock": "^3.0.0", "vite-plugin-pwa": "0.16.4", "vite-plugin-semi-theme": "^0.5.0", "vite-plugin-static-copy": "^1.0.5", "vite-tsconfig-paths": "4.2.0", "vitest": "^3.2.4", "whatwg-fetch": "3.6.17", "workbox-build": "7.0.0", "workbox-window": "7.0.0"}, "browserslist": {"production": "Edge >= 18, Firefox >= 60, Chrome >= 61, Safari >= 11, Opera >= 48", "development": ["last 1 chrome version", "last 1 firefox version"]}, "pnpm": {"overrides": {"headers-polyfill": "3.1.2"}}}