import {
  IconCheckCircleStroked,
  IconCloud,
  IconFlag,
} from "@douyinfe/semi-icons";
import { Badge, Typography } from "@douyinfe/semi-ui";
import React from "react";
import { useRegionPluginConfigContentList } from "./useRegionPluginConfigList";

export interface RegionPluginInfo {
  id: number;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  version: string;
  lastUpdate: string;
  enabled?: boolean;
}

interface RegionPluginCardProps {
  plugin: RegionPluginInfo;
  selected: boolean;
  onClick: () => void;
}

export const REGION_PLUGINS: RegionPluginInfo[] = [
  {
    id: 1,
    name: "云南",
    description: "配置云南地区特有的上报参数",
    icon: <IconCloud />,
    color: "#12A182",
    version: "v1.2.0",
    lastUpdate: "2024-12-15",
    enabled: true,
  },
  {
    id: 2,
    name: "江西-新干盐化城",
    description: "配置江西新干盐化城特有的上报参数",
    icon: <IconFlag />,
    color: "#0072E5",
    version: "v1.0.5",
    lastUpdate: "2025-02-27",
    enabled: true,
  },
  {
    id: 3,
    name: "江西-万年凤巢工业区",
    description: "配置江西万年凤巢工业区特有的上报参数",
    icon: <IconFlag />,
    color: "#FFC107",
    version: "v1.0.0",
    lastUpdate: "2025-06-20",
    enabled: true,
  },
];

export const CurrentRegionPluginConfigContentList = Object.values(
  useRegionPluginConfigContentList()
);

const RegionPluginCard: React.FC<RegionPluginCardProps> = ({
  plugin,
  selected,
  onClick,
}) => {
  return (
    <div
      className={`
        relative overflow-hidden rounded p-1.5 cursor-pointer transition-all
        ${
          selected
            ? "border shadow-sm"
            : "border border-gray-200 hover:border-gray-300 bg-white"
        }
      `}
      style={{
        borderColor: selected ? plugin.color : undefined,
        backgroundColor: selected ? `${plugin.color}08` : undefined,
      }}
      onClick={onClick}
    >
      {/* More compact layout with horizontal arrangement */}
      <div className="flex items-start">
        {/* Icon - Smaller */}
        <div
          className="p-1.5 rounded-full mr-2 shrink-0 flex items-center justify-center"
          style={{
            backgroundColor: `${plugin.color}15`,
            color: plugin.color,
          }}
        >
          <div style={{ fontSize: "14px" }}>{plugin.icon}</div>
        </div>

        {/* Content - More compact */}
        <div className="flex-1 min-w-0 py-0.5">
          {/* Title row with badge */}
          <div className="flex items-center mb-0.5">
            <h3 className="text-sm font-medium">{plugin.name}</h3>
            {plugin.enabled && (
              <Badge dot type="success" style={{ marginLeft: "4px" }} />
            )}
            {selected && (
              <div
                className="ml-auto flex items-center"
                style={{ color: plugin.color, fontSize: "12px" }}
              >
                <IconCheckCircleStroked size="extra-small" />
                <span className="ml-0.5 text-xs">使用中</span>
              </div>
            )}
          </div>

          {/* Version and update info */}
          <div className="flex justify-between items-center text-xs text-gray-500 mb-0.5">
            <span>{plugin.version}</span>
            <span className="text-xs truncate">更新: {plugin.lastUpdate}</span>
          </div>

          {/* Description - Single line with ellipsis */}
          <Typography.Text
            ellipsis={{ rows: 1 }}
            className="text-xs text-gray-600"
          >
            {plugin.description}
          </Typography.Text>
        </div>
      </div>
    </div>
  );
};

export default RegionPluginCard;
