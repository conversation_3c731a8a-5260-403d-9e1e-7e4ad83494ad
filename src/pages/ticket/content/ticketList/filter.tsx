import { IconSearch } from "@douyinfe/semi-icons";
import { Form, useFormApi } from "@douyinfe/semi-ui";
import { useToggle } from "ahooks";
import { jobSliceFilterAtom } from "atoms/specialWork";
import {
  DepartmentSearch,
  EmployeeSearch,
  JOB_REPORT_STATUS_MAP,
  JOB_SLICE_STATUS,
  JobCategorySelect,
  RestSelect,
  UNIT_CATEGORY_MAP,
} from "components";
import { useFilterSearch } from "hooks";

const ComponentUsingFormApi = ({ handleReset }) => {
  const formApi = useFormApi();

  return (
    <div className="flex gap-2">
      <span
        className="btn rounded btn-primary btn-sm"
        onClick={() => {
          formApi.submitForm();
        }}
      >
        查&nbsp;&nbsp;询
      </span>
      <span
        className="btn btn-sm rounded"
        onClick={() => {
          formApi.reset();
          handleReset();
        }}
      >
        重&nbsp;&nbsp;置
      </span>
    </div>
  );
};

export function TicketListFilter({ mode, filter }) {
  const [state, { toggle }] = useToggle(true);
  const [handleSearch, handleReset] = useFilterSearch(jobSliceFilterAtom);

  return (
    <div className="big_screen_table_filter_box flex flex-col bg-white shadow rounded relative ">
      <div className="big_screen_table_filter_title text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="关键字搜索"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <DepartmentSearch field="departmentId" placeholder="申请单位" />
          <EmployeeSearch
            field="applyPersonId"
            placeholder="申请人"
            filter={filter}
          />
          {filter?.unitCategory ? (
            <Form.Select
              placeholder="作业单位类型"
              field="unitCategory"
              noLabel
              className="w-full"
              initValue={filter?.unitCategory}
              disabled
            >
              {UNIT_CATEGORY_MAP.map((item) => (
                <Form.Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Form.Select.Option>
              ))}
            </Form.Select>
          ) : (
            <Form.Select
              placeholder="作业单位类型"
              field="unitCategory"
              noLabel
              className="w-full"
            >
              {UNIT_CATEGORY_MAP.map((item) => (
                <Form.Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Form.Select.Option>
              ))}
            </Form.Select>
          )}

          {state ? null : (
            <>
              {mode != "modal" ? (
                <JobCategorySelect field="categoryId" placeholder="作业类型" />
              ) : null}
              <RestSelect
                options={JOB_SLICE_STATUS}
                placeholder="作业票状态"
                field="statusSliceIn"
                multiple
              />
              {/* <Form.DatePicker
                  placeholder="预约开始时间"
                  noLabel
                  field="applyTimeGte"
                  className="w-full"
                  density="compact"
                />
                <Form.DatePicker
                  placeholder="预约结束时间"
                  noLabel
                  className="w-full"
                  field="applyTimeLt"
                  density="compact"
                /> */}
              <Form.DatePicker
                placeholder={["申请开始时间", "申请结束时间"]}
                noLabel
                field="applyTime"
                type="dateTimeRange"
                className="w-full"
                // density="compact"
                // style={{ width: 347 }}
                position="bottomRight"
              />
              <Form.DatePicker
                placeholder={["作业开始时间", "作业结束时间"]}
                noLabel
                field="beginTime"
                type="dateTimeRange"
                className="w-full"
                // density="compact"
                // style={{ width: 347 }}
                position="bottomRight"
              />
              {/* <Form.DatePicker
                  placeholder="作业开始时间"
                  noLabel
                  className="w-full"
                  field="beginTimeGte"
                  density="compact"
                />
                <Form.DatePicker
                  placeholder="作业结束时间"
                  noLabel
                  className="w-full"
                  field="beginTimeLt"
                  density="compact"
                /> */}
            </>
          )}
          <Form.Select
            placeholder="上报状态"
            field="reportStatus"
            noLabel
            className="w-full"
          >
            {JOB_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="报备上报状态"
            field="backupReportStatus"
            noLabel
            className="w-full"
          >
            {JOB_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="流程上报状态"
            field="progressReportStatus"
            noLabel
            className="w-full"
          >
            {JOB_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <ComponentUsingFormApi handleReset={handleReset} />

          <span
            className="w-10 h-4 bg-white big_screen_table_filter_toggle rounded-2xl shadow border absolute bottom-[-6px] left-2/4 ml-[-20px] flex items-center justify-center cursor-pointer"
            onClick={toggle}
          >
            {state ? (
              <i className="ri-arrow-down-s-line" />
            ) : (
              <i className="ri-arrow-up-s-line" />
            )}
          </span>
        </Form>
      </div>
    </div>
  );
}
