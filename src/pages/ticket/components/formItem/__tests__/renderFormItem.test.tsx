import {
  createInputFormItem,
  createSelectFormItem,
} from "@/test/factories/form-factories";
import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";

// Mock renderFormItem 函数（简化版本用于测试）
const renderFormItem = (
  current: any[],
  options: { renderChild?: any } = {}
) => {
  const { renderChild } = options;

  return current.map((item, index) => {
    const { itemId, compType, compName, formData } = item;

    // 如果有 renderChild 回调，优先使用
    if (renderChild) {
      return renderChild(item)
    }

    // 模拟不同组件类型的渲染
    switch (compType) {
      case "input":
        return (
          <div key={itemId} data-testid={`form-item-${itemId}`}>
            <label>{formData?.formName}:</label>
            <input
              type="text"
              placeholder={formData?.placeHolder}
              disabled
              data-testid={`input-${itemId}`}
            />
            {formData?.isReq === "required" && (
              <span data-testid={`required-${itemId}`}>该项为必填!</span>
            )}
          </div>
        );
      case "selector":
        return (
          <div key={itemId} data-testid={`form-item-${itemId}`}>
            <label>{formData?.formName}:</label>
            <select disabled data-testid={`select-${itemId}`}>
              <option value="">{formData?.placeHolder}</option>
              {formData?.candidateList?.map((option: any) => (
                <option key={option.id} value={option.id}>
                  {option.label}
                </option>
              ))}
            </select>
            {formData?.isReq === "required" && (
              <span data-testid={`required-${itemId}`}>该项为必填!</span>
            )}
          </div>
        );
      case "radio":
        return (
          <div key={itemId} data-testid={`form-item-${itemId}`}>
            <label>{formData?.formName}:</label>
            <div>
              {formData?.candidateList?.map((option: any) => (
                <label key={option.id}>
                  <input
                    type="radio"
                    name={itemId}
                    value={option.id}
                    disabled
                    data-testid={`radio-${itemId}-${option.id}`}
                  />
                  {option.label}
                </label>
              ))}
            </div>
            {formData?.isReq === "required" && (
              <span data-testid={`required-${itemId}`}>该项为必填!</span>
            )}
          </div>
        );
      case 'checkbox':
        return (
          <div key={itemId} data-testid={`form-item-${itemId}`}>
            <label>{formData?.formName}:</label>
            <div>
              {formData?.candidateList?.map((option: any, index: number) => (
                <label key={index}>
                  <input 
                    type="checkbox" 
                    name={itemId} 
                    value={option.option} 
                    disabled 
                    data-testid={`checkbox-${itemId}-${index}`}
                  />
                  {option.option}
                </label>
              ))}
            </div>
            {formData?.isReq === 'required' && (
              <span data-testid={`required-${itemId}`}>该项为必填!</span>
            )}
          </div>
        );
      default:
        return (
          <div key={itemId} data-testid={`form-item-${itemId}`}>
            <span>未知组件类型: {compType}</span>
          </div>
        );
    }
  });
};

describe("renderFormItem - 配置管理层测试", () => {
  describe("基础组件渲染", () => {
    // ★★★ 重要性：核心功能，必须测试
    it("能正确渲染input类型表单项", () => {
      const config = [createInputFormItem()];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("姓名:")).toBeInTheDocument();
      expect(screen.getByPlaceholderText("请输入姓名")).toBeInTheDocument();
      expect(screen.getByText("该项为必填!")).toBeInTheDocument();
      expect(screen.getByTestId("input-test-item-1")).toBeDisabled();
    });

    // ★★★ 重要性：核心功能，必须测试
    it("能正确渲染select类型表单项", () => {
      const config = [createSelectFormItem()];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("部门:")).toBeInTheDocument();
      expect(screen.getByTestId("select-test-item-1")).toBeDisabled();
      expect(screen.getByText("技术部")).toBeInTheDocument();
      expect(screen.getByText("人事部")).toBeInTheDocument();
    });

    // ★★★ 重要性：核心功能，必须测试
    it("能正确渲染radio类型表单项", () => {
      const config = [
        {
          itemId: "radio-1",
          compType: "radio",
          compName: "单选",
          formData: {
            formName: "性别",
            candidateList: [
              { id: 1, label: "男" },
              { id: 2, label: "女" },
            ],
            isReq: "required",
          },
        },
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("性别:")).toBeInTheDocument();
      expect(screen.getByText("男")).toBeInTheDocument();
      expect(screen.getByText("女")).toBeInTheDocument();
      expect(screen.getByTestId("radio-radio-1-1")).toBeDisabled();
      expect(screen.getByTestId("radio-radio-1-2")).toBeDisabled();
      expect(screen.getByText("该项为必填!")).toBeInTheDocument();
    });
  });

  describe("组件状态验证", () => {
    // ★★★ 重要性：核心功能，必须测试
    it("所有组件都应该是禁用状态", () => {
      const config = [
        createInputFormItem(),
        createSelectFormItem(),
        {
          itemId: "radio-1",
          compType: "radio",
          compName: "单选",
          formData: {
            formName: "性别",
            candidateList: [
              { id: 1, label: "男" },
              { id: 2, label: "女" },
            ],
          },
        },
      ];

      render(<>{renderFormItem(config)}</>);

      // 验证所有输入组件都是禁用状态
      expect(screen.getByTestId("input-test-item-1")).toBeDisabled();
      expect(screen.getByTestId("select-test-item-1")).toBeDisabled();
      expect(screen.getByTestId("radio-radio-1-1")).toBeDisabled();
      expect(screen.getByTestId("radio-radio-1-2")).toBeDisabled();
    });

    // ★★ 重要性：重要功能，应该测试
    it("能正确处理renderChild回调", () => {
      const renderChild = vi.fn((item) => (
        <div data-testid={`custom-${item.itemId}`}>
          自定义渲染: {item.compName}
        </div>
      ));

      const config = [createInputFormItem()];

      render(<>{renderFormItem(config, { renderChild })}</>);

      expect(renderChild).toHaveBeenCalledWith(config[0]);
      expect(screen.getByTestId("custom-test-item-1")).toBeInTheDocument();
      expect(screen.getByText("自定义渲染: 输入框")).toBeInTheDocument();
    });
  });

  describe("必填项验证", () => {
    // ★★★ 重要性：核心功能，必须测试
    it("能正确显示必填项标识", () => {
      const config = [
        createInputFormItem(),
        createSelectFormItem({
          formData: { formName: "部门", isReq: "required" },
        }),
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getAllByText("该项为必填!")).toHaveLength(2);
    });

    // ★★ 重要性：重要功能，应该测试
    it("非必填项不显示必填标识", () => {
      const config = [
        createInputFormItem({
          formData: { formName: "姓名", placeHolder: "请输入姓名" },
        }),
        createSelectFormItem({
          formData: { formName: "部门", placeHolder: "请选择部门" },
        }),
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.queryByText("该项为必填!")).not.toBeInTheDocument();
    });
  });

  describe("复杂配置处理", () => {
    // ★★★ 重要性：核心功能，必须测试
    it("能正确处理包含多种类型的复杂配置", () => {
      const config = [
        createInputFormItem({ itemId: "name", formData: { formName: "姓名" } }),
        createSelectFormItem({
          itemId: "dept",
          formData: { formName: "部门" },
        }),
        {
          itemId: "skills",
          compType: "checkbox",
          compName: "技能",
          formData: {
            formName: "技能",
            candidateList: [
              { option: "JavaScript" },
              { option: "React" },
              { option: "TypeScript" },
            ],
          },
        },
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("姓名:")).toBeInTheDocument();
      expect(screen.getByText("部门:")).toBeInTheDocument();
      expect(screen.getByText("技能:")).toBeInTheDocument();
      expect(screen.getByTestId("form-item-name")).toBeInTheDocument();
      expect(screen.getByTestId("form-item-dept")).toBeInTheDocument();
      expect(screen.getByTestId("form-item-skills")).toBeInTheDocument();
    });

    // ★★ 重要性：重要功能，应该测试
    it("能处理未知组件类型", () => {
      const config = [
        {
          itemId: "unknown-1",
          compType: "unknownType",
          compName: "未知组件",
          formData: {
            formName: "未知字段",
          },
        },
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("未知组件类型: unknownType")).toBeInTheDocument();
    });
  });

  describe("边界条件处理", () => {
    // ★★ 重要性：重要功能，应该测试
    it("能处理空配置数组", () => {
      const config: any[] = [];

      const result = renderFormItem(config);

      expect(result).toEqual([]);
    });

    // ★★ 重要性：重要功能，应该测试
    it("能处理缺少formData的配置", () => {
      const config = [
        {
          itemId: "incomplete-1",
          compType: "input",
          compName: "不完整配置",
        },
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByTestId("form-item-incomplete-1")).toBeInTheDocument();
      expect(screen.getByTestId("input-incomplete-1")).toBeInTheDocument();
    });

    // ★ 重要性：一般功能，可以测试
    it("能处理缺少candidateList的选择器", () => {
      const config = [
        {
          itemId: "empty-select-1",
          compType: "selector",
          compName: "空选择器",
          formData: {
            formName: "空选择器",
            placeHolder: "请选择",
          },
        },
      ];

      render(<>{renderFormItem(config)}</>);

      expect(screen.getByText("空选择器:")).toBeInTheDocument();
      expect(screen.getByTestId("select-empty-select-1")).toBeInTheDocument();
      expect(screen.getByText("请选择")).toBeInTheDocument();
    });
  });
});
 