import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock 所有依赖
vi.mock("@douyinfe/semi-ui", () => ({
  Form: {
    Input: ({ placeholder, ...props }: any) => (
      <input data-testid="semi-input" placeholder={placeholder} {...props} />
    ),
    Select: ({ children, ...props }: any) => (
      <select data-testid="semi-select" {...props}>
        {children}
      </select>
    ),
    DatePicker: ({ ...props }: any) => (
      <input data-testid="semi-datepicker" type="date" {...props} />
    ),
    TimePicker: ({ ...props }: any) => (
      <input data-testid="semi-timepicker" type="time" {...props} />
    ),
    TextArea: ({ placeholder, ...props }: any) => (
      <textarea
        data-testid="semi-textarea"
        placeholder={placeholder}
        {...props}
      />
    ),
    Checkbox: ({ ...props }: any) => (
      <input data-testid="semi-checkbox" type="checkbox" {...props} />
    ),
    Radio: ({ ...props }: any) => (
      <input data-testid="semi-radio" type="radio" {...props} />
    ),
    Switch: ({ ...props }: any) => (
      <input data-testid="semi-switch" type="checkbox" {...props} />
    ),
    Upload: ({ ...props }: any) => <div data-testid="semi-upload" {...props} />,
    Table: ({ ...props }: any) => <table data-testid="semi-table" {...props} />,
  },
  useFormApi: () => ({
    getValue: vi.fn((field: string) => {
      const values: Record<string, any> = {
        "form.level": 1,
        "form.isUpgrade": 1,
        "form.unitCategory": 1,
      };
      return values[field] || "";
    }),
    setValue: vi.fn(),
  }),
  Tooltip: ({ children, ...props }: any) => (
    <div data-testid="semi-tooltip" {...props}>
      {children}
    </div>
  ),
}));

// Mock 整个 components 模块
vi.mock("components", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as any),
    AreaSearch: ({ ...props }: any) => (
      <div data-testid="area-search" {...props} />
    ),
    ContractorSearch: ({ ...props }: any) => (
      <div data-testid="contractor-search" {...props} />
    ),
    DepartmentPicker: ({ ...props }: any) => (
      <div data-testid="department-picker" {...props} />
    ),
    EmployeePicker: ({ ...props }: any) => (
      <div data-testid="employee-picker" {...props} />
    ),
    EmployeeSearch: ({ ...props }: any) => (
      <div data-testid="employee-search" {...props} />
    ),
    MapPicker: ({ ...props }: any) => (
      <div data-testid="map-picker" {...props} />
    ),
    Upload: ({ ...props }: any) => (
      <div data-testid="upload-component" {...props} />
    ),
    RISK_MEASURE_ACCIDENTTYPE: [{ id: 1, name: "测试风险类型" }],
  };
});

vi.mock("components/upload", () => ({
  Upload: ({ ...props }: any) => (
    <div data-testid="upload-component" {...props} />
  ),
}));

// Mock 其他依赖
vi.mock("jotai", () => ({
  useAtom: () => [vi.fn(), vi.fn()],
  useAtomValue: () => vi.fn(),
}));

vi.mock("@tanstack/react-query", () => ({
  useQuery: () => ({
    data: [],
    isLoading: false,
    error: null,
  }),
}));

vi.mock("ramda", () => ({
  find: vi.fn(() => ({ name: "测试风险" })),
  propEq: vi.fn(() => vi.fn()),
  pick: vi.fn(() => ({ id: 1, name: "测试", type: 1 })),
}));

// Mock 常量
vi.mock("../../../../../../constants/riskMeasure", () => ({
  riskMeasureOptions: [
    { id: 1, accidentType: 1, safetyMeasure: "测试安全措施" },
  ],
  RISK_MEASURE_ACCIDENTTYPE: [{ id: 1, name: "测试风险类型" }],
}));

vi.mock("config", () => ({
  RoleKeyCode: {},
  RoleKeyPath: {},
}));

vi.mock("atoms", () => ({
  certificateSelectAtom: {},
  mapPickerAtom: {},
}));

vi.mock("components", () => ({
  RISK_MEASURE_ACCIDENTTYPE: [
    { label: "类型1", value: 1 },
    { label: "类型2", value: 2 },
  ],
}));

vi.mock("./renderTable", () => ({
  RenderTable: ({ data, ...props }: any) => (
    <div data-testid="render-table" {...props}>
      {data?.map((item: any, index: number) => (
        <div key={index} data-testid={`table-row-${index}`}>
          {JSON.stringify(item)}
        </div>
      ))}
    </div>
  ),
}));

// 现在导入真正的组件
import { RenderItem } from "../renderItem";

describe("RenderItem 核心功能测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // 基础组件渲染测试
  describe("基础组件渲染测试", () => {
    it("应该正确渲染 plainText 组件", () => {
      const item = {
        compType: "plainText",
        formData: {
          actualValue: "测试文本",
          textType: "title",
          textAlign: "center",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByText("测试文本")).toBeInTheDocument();
    });

    it("应该正确渲染 input 组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试输入",
          placeHolder: "请输入",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该正确渲染 select 组件", () => {
      const item = {
        compType: "select",
        formData: {
          formName: "测试选择",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-select")).toBeInTheDocument();
    });

    it("应该正确渲染 textarea 组件", () => {
      const item = {
        compType: "textarea",
        formData: {
          formName: "测试文本域",
          placeHolder: "请输入内容",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-textarea")).toBeInTheDocument();
    });

    it("应该正确渲染 datePicker 组件", () => {
      const item = {
        compType: "datePicker",
        formData: {
          formName: "测试日期",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-datepicker")).toBeInTheDocument();
    });

    it("应该正确渲染 timePicker 组件", () => {
      const item = {
        compType: "timePicker",
        formData: {
          formName: "测试时间",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-timepicker")).toBeInTheDocument();
    });

    it("应该正确渲染 checkbox 组件", () => {
      const item = {
        compType: "checkbox",
        formData: {
          formName: "测试复选框",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-checkbox")).toBeInTheDocument();
    });

    it("应该正确渲染 radio 组件", () => {
      const item = {
        compType: "radio",
        formData: {
          formName: "测试单选",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-radio")).toBeInTheDocument();
    });

    it("应该正确渲染 switch 组件", () => {
      const item = {
        compType: "switch",
        formData: {
          formName: "测试开关",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-switch")).toBeInTheDocument();
    });

    it("应该正确渲染 upload 组件", () => {
      const item = {
        compType: "upload",
        formData: {
          formName: "测试上传",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-upload")).toBeInTheDocument();
    });
  });

  // 特殊组件渲染测试
  describe("特殊组件渲染测试", () => {
    it("应该正确渲染 AreaSearch 组件", () => {
      const item = {
        compType: "AreaSearch",
        formData: {
          formName: "区域搜索",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("area-search")).toBeInTheDocument();
    });

    it("应该正确渲染 ContractorSearch 组件", () => {
      const item = {
        compType: "ContractorSearch",
        formData: {
          formName: "承包商搜索",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("contractor-search")).toBeInTheDocument();
    });

    it("应该正确渲染 DepartmentPicker 组件", () => {
      const item = {
        compType: "DepartmentPicker",
        formData: {
          formName: "部门选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("department-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 EmployeePicker 组件", () => {
      const item = {
        compType: "EmployeePicker",
        formData: {
          formName: "员工选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("employee-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 EmployeeSearch 组件", () => {
      const item = {
        compType: "EmployeeSearch",
        formData: {
          formName: "员工搜索",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("employee-search")).toBeInTheDocument();
    });

    it("应该正确渲染 MapPicker 组件", () => {
      const item = {
        compType: "MapPicker",
        formData: {
          formName: "地图选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("map-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 Upload 组件", () => {
      const item = {
        compType: "Upload",
        formData: {
          formName: "文件上传",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("upload-component")).toBeInTheDocument();
    });
  });

  // 表格组件渲染测试
  describe("表格组件渲染测试", () => {
    it("应该正确渲染 table 组件", () => {
      const item = {
        compType: "table",
        formData: {
          formName: "测试表格",
          columns: [
            { title: "姓名", dataIndex: "name" },
            { title: "年龄", dataIndex: "age" },
          ],
          dataSource: [
            { name: "张三", age: 25 },
            { name: "李四", age: 30 },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-table")).toBeInTheDocument();
      expect(screen.getByText("张三")).toBeInTheDocument();
      expect(screen.getByText("李四")).toBeInTheDocument();
    });
  });

  // 条件显示逻辑测试
  describe("条件显示逻辑测试", () => {
    it("应该根据条件显示组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "条件输入",
        },
        rule: [
          {
            field: "form.level",
            operator: "eq",
            value: 1,
          },
        ],
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 由于 mock 的 getValue 返回 1，所以组件应该显示
      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该处理高处作业验证逻辑", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "高处作业验证",
          business: "level",
        },
        rule: [
          {
            field: "form.level",
            operator: "gte",
            value: 2,
          },
        ],
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 由于 mock 的 getValue 返回 1，所以组件应该隐藏
      expect(screen.queryByTestId("semi-input")).not.toBeInTheDocument();
    });
  });

  // 字段名称处理测试
  describe("字段名称处理测试", () => {
    it("应该使用 business 字段作为字段名", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试字段",
          business: "testField",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该使用 itemId 作为字段名（当没有 business 时）", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试字段",
        },
        itemId: "testItemId",
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该使用 compName 作为显示名称", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试字段",
          compName: "自定义显示名称",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });
  });

  // 验证逻辑测试
  describe("验证逻辑测试", () => {
    it("应该处理必填字段验证", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "必填字段",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该处理数字类型验证", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "数字字段",
          inputType: "number",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });
  });

  // 添加更多能提升覆盖率的测试用例
  describe("覆盖率提升测试", () => {
    it("应该处理不同的 compType 类型", () => {
      const types = ["plainText", "input", "select", "textarea", "datePicker"];
      types.forEach((type) => {
        const item = {
          compType: type,
          formData: { actualValue: `测试${type}`, formName: `测试${type}` },
        };
        render(<RenderItem item={item} k={1} rule={[]} />);
        expect(screen.getByText(`测试${type}`)).toBeInTheDocument();
      });
    });

    it("应该处理 employeePicker 组件的不同分支", () => {
      // 测试分支1：满足条件并且不限制人员
      const item1 = {
        compType: "employeePicker",
        formData: {
          formName: "员工选择器1",
          serviceRange: [2, 3],
          candidateList: [],
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("员工选择器1")).toBeInTheDocument();

      // 测试分支2：满足条件但是限制人员-渲染为select
      const item2 = {
        compType: "employeePicker",
        formData: {
          formName: "员工选择器2",
          serviceRange: [2, 3],
          candidateList: [{ id: 1, name: "张三", type: 1 }],
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("员工选择器2")).toBeInTheDocument();
    });

    it("应该处理 riskPicker 组件", () => {
      const item = {
        compType: "riskPicker",
        formData: {
          formName: "风险选择器",
          serviceRange: [1, 2],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);
      expect(screen.getByText("风险选择器")).toBeInTheDocument();
    });

    it("应该处理 mapPicker 组件", () => {
      const item = {
        compType: "mapPicker",
        formData: {
          formName: "地图选择器",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);
      expect(screen.getByText("地图选择器")).toBeInTheDocument();
    });

    it("应该处理 annexImgPicker 组件", () => {
      const item = {
        compType: "annexImgPicker",
        formData: {
          formName: "图片附件",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);
      expect(screen.getByText("图片附件")).toBeInTheDocument();
    });

    it("应该处理 annexFilePicker 组件", () => {
      const item = {
        compType: "annexFilePicker",
        formData: {
          formName: "文件附件",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);
      expect(screen.getByText("文件附件")).toBeInTheDocument();
    });

    it("应该处理 table 组件的不同情况", () => {
      // 测试分支1：有数据的情况
      const item1 = {
        compType: "table",
        formData: {
          formName: "表格1",
          tableData: [
            { id: 1, name: "张三", age: 25 },
            { id: 2, name: "李四", age: 30 },
          ],
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("表格1")).toBeInTheDocument();

      // 测试分支2：无数据的情况
      const item2 = {
        compType: "table",
        formData: {
          formName: "表格2",
          tableData: [],
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("表格2")).toBeInTheDocument();
    });

    it("应该处理特殊组件的不同分支", () => {
      // 测试 areaSearch 组件
      const item1 = {
        compType: "areaSearch",
        formData: {
          formName: "区域搜索",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("区域搜索")).toBeInTheDocument();

      // 测试 contractorSearch 组件
      const item2 = {
        compType: "contractorSearch",
        formData: {
          formName: "承包商搜索",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("承包商搜索")).toBeInTheDocument();

      // 测试 departmentPicker 组件
      const item3 = {
        compType: "departmentPicker",
        formData: {
          formName: "部门选择器",
        },
      };

      render(<RenderItem item={item3} k={1} rule={[]} />);
      expect(screen.getByText("部门选择器")).toBeInTheDocument();

      // 测试 employeeSearch 组件
      const item4 = {
        compType: "employeeSearch",
        formData: {
          formName: "员工搜索",
        },
      };

      render(<RenderItem item={item4} k={1} rule={[]} />);
      expect(screen.getByText("员工搜索")).toBeInTheDocument();
    });

    it("应该处理验证规则的不同情况", () => {
      // 测试有验证规则的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "带验证的输入框",
          validationRules: [
            { required: true, message: "此项为必填项" },
            { pattern: /^[0-9]+$/, message: "只能输入数字" },
          ],
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("带验证的输入框")).toBeInTheDocument();

      // 测试无验证规则的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "无验证的输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无验证的输入框")).toBeInTheDocument();
    });

    it("应该处理条件显示逻辑", () => {
      // 测试满足显示条件的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "条件显示输入框1",
          showCondition: "form.level === 1",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("条件显示输入框1")).toBeInTheDocument();

      // 测试不满足显示条件的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "条件显示输入框2",
          showCondition: "form.level === 999", // 不满足条件
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      // 这个组件应该不显示，所以不会找到文本
    });

    it("应该处理组件属性的不同情况", () => {
      // 测试有特殊属性的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "特殊属性输入框",
          placeholder: "请输入内容",
          maxLength: 100,
          disabled: true,
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("特殊属性输入框")).toBeInTheDocument();

      // 测试无特殊属性的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "普通输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("普通输入框")).toBeInTheDocument();
    });

    it("应该处理表格数据的不同结构", () => {
      // 测试复杂表格数据结构
      const item1 = {
        compType: "table",
        formData: {
          formName: "复杂表格",
          tableData: [
            {
              id: 1,
              name: "张三",
              department: "技术部",
              position: "工程师",
              salary: 10000,
            },
            {
              id: 2,
              name: "李四",
              department: "产品部",
              position: "产品经理",
              salary: 15000,
            },
          ],
          columns: [
            { title: "姓名", dataIndex: "name" },
            { title: "部门", dataIndex: "department" },
            { title: "职位", dataIndex: "position" },
            { title: "薪资", dataIndex: "salary" },
          ],
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("复杂表格")).toBeInTheDocument();

      // 测试简单表格数据结构
      const item2 = {
        compType: "table",
        formData: {
          formName: "简单表格",
          tableData: [{ id: 1, name: "测试" }],
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("简单表格")).toBeInTheDocument();
    });

    it("应该处理布局配置的不同情况", () => {
      // 测试有布局配置的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "布局配置输入框",
          layout: {
            span: 12,
            offset: 6,
            style: { marginBottom: 16 },
          },
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("布局配置输入框")).toBeInTheDocument();

      // 测试无布局配置的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "无布局配置输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无布局配置输入框")).toBeInTheDocument();
    });

    it("应该处理业务逻辑的不同情况", () => {
      // 测试有业务逻辑的情况
      const item1 = {
        compType: "input",
        business: "level",
        formData: {
          formName: "业务逻辑输入框",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("业务逻辑输入框")).toBeInTheDocument();

      // 测试无业务逻辑的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "无业务逻辑输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无业务逻辑输入框")).toBeInTheDocument();
    });

    it("应该处理字段命名的不同情况", () => {
      // 测试有字段名的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "有字段名输入框",
          fieldName: "testField",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("有字段名输入框")).toBeInTheDocument();

      // 测试无字段名的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "无字段名输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无字段名输入框")).toBeInTheDocument();
    });

    it("应该处理高处作业验证逻辑", () => {
      // 测试高处作业验证的不同情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "高处作业验证1",
          business: "form.level",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} isHighWork={true} />);
      expect(screen.getByText("高处作业验证1")).toBeInTheDocument();

      // 测试非高处作业的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "非高处作业验证",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} isHighWork={false} />);
      expect(screen.getByText("非高处作业验证")).toBeInTheDocument();
    });

    it("应该处理验证函数的不同情况", () => {
      // 测试有验证函数的情况
      const item1 = {
        compType: "input",
        formData: {
          formName: "有验证函数输入框",
          validation: "asyncValidate",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} isHighWork={true} />);
      expect(screen.getByText("有验证函数输入框")).toBeInTheDocument();

      // 测试无验证函数的情况
      const item2 = {
        compType: "input",
        formData: {
          formName: "无验证函数输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无验证函数输入框")).toBeInTheDocument();
    });

    it("应该处理表格组件的不同配置", () => {
      // 测试表格组件的不同配置
      const item1 = {
        compType: "table",
        formData: {
          formName: "表格配置1",
          tableData: [{ id: 1, name: "张三" }],
          columns: [{ title: "姓名", dataIndex: "name" }],
          pagination: { pageSize: 10 },
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} isTable={true} />);
      expect(screen.getByText("表格配置1")).toBeInTheDocument();

      // 测试非表格的情况
      const item2 = {
        compType: "table",
        formData: {
          formName: "非表格配置",
          tableData: [{ id: 1, name: "李四" }],
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} isTable={false} />);
      expect(screen.getByText("非表格配置")).toBeInTheDocument();
    });

    it("应该处理特殊组件的不同配置", () => {
      // 测试特殊组件的不同配置
      const item1 = {
        compType: "areaSearch",
        formData: {
          formName: "区域搜索配置1",
          showOptions: true,
          showBtn: true,
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("区域搜索配置1")).toBeInTheDocument();

      // 测试不同配置的特殊组件
      const item2 = {
        compType: "contractorSearch",
        formData: {
          formName: "承包商搜索配置2",
          showOptions: false,
          showBtn: false,
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("承包商搜索配置2")).toBeInTheDocument();
    });

    it("应该处理组件回调函数", () => {
      // 测试有回调函数的组件
      const item1 = {
        compType: "employeePicker",
        formData: {
          formName: "员工选择器回调",
          serviceRange: [2, 3],
          candidateList: [],
          callback: (list: any) => {
            console.log("回调函数执行", list);
          },
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("员工选择器回调")).toBeInTheDocument();

      // 测试无回调函数的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "无回调函数输入框",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("无回调函数输入框")).toBeInTheDocument();
    });

    it("应该处理组件的不同状态", () => {
      // 测试组件的不同状态
      const item1 = {
        compType: "input",
        formData: {
          formName: "状态输入框1",
          disabled: true,
          readonly: false,
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("状态输入框1")).toBeInTheDocument();

      // 测试不同状态的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "状态输入框2",
          disabled: false,
          readonly: true,
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("状态输入框2")).toBeInTheDocument();
    });

    it("应该处理组件的不同样式", () => {
      // 测试组件的不同样式
      const item1 = {
        compType: "input",
        formData: {
          formName: "样式输入框1",
          style: { color: "red", fontSize: "14px" },
          className: "custom-class",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("样式输入框1")).toBeInTheDocument();

      // 测试不同样式的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "样式输入框2",
          style: { backgroundColor: "blue" },
          className: "another-class",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("样式输入框2")).toBeInTheDocument();
    });

    it("应该处理组件的不同事件", () => {
      // 测试组件的不同事件
      const item1 = {
        compType: "input",
        formData: {
          formName: "事件输入框1",
          onChange: (value: any) => {
            console.log("onChange事件", value);
          },
          onBlur: (value: any) => {
            console.log("onBlur事件", value);
          },
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("事件输入框1")).toBeInTheDocument();

      // 测试不同事件的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "事件输入框2",
          onClick: (value: any) => {
            console.log("onClick事件", value);
          },
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("事件输入框2")).toBeInTheDocument();
    });

    it("应该处理组件的不同数据源", () => {
      // 测试组件的不同数据源
      const item1 = {
        compType: "select",
        formData: {
          formName: "数据源选择器1",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
          dataSource: "static",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("数据源选择器1")).toBeInTheDocument();

      // 测试不同数据源的组件
      const item2 = {
        compType: "select",
        formData: {
          formName: "数据源选择器2",
          dataSource: "api",
          apiUrl: "/api/options",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("数据源选择器2")).toBeInTheDocument();
    });

    it("应该处理组件的不同验证规则", () => {
      // 测试组件的不同验证规则
      const item1 = {
        compType: "input",
        formData: {
          formName: "验证规则输入框1",
          rules: [
            { required: true, message: "必填项" },
            { pattern: /^[0-9]+$/, message: "只能输入数字" },
          ],
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("验证规则输入框1")).toBeInTheDocument();

      // 测试不同验证规则的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "验证规则输入框2",
          rules: [
            { min: 3, message: "最少3个字符" },
            { max: 10, message: "最多10个字符" },
          ],
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("验证规则输入框2")).toBeInTheDocument();
    });

    it("应该处理组件的不同默认值", () => {
      // 测试组件的不同默认值
      const item1 = {
        compType: "input",
        formData: {
          formName: "默认值输入框1",
          defaultValue: "默认文本",
          value: "当前值",
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("默认值输入框1")).toBeInTheDocument();

      // 测试不同默认值的组件
      const item2 = {
        compType: "select",
        formData: {
          formName: "默认值选择器2",
          defaultValue: "option1",
          value: "option2",
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("默认值选择器2")).toBeInTheDocument();
    });

    it("应该处理组件的不同显示模式", () => {
      // 测试组件的不同显示模式
      const item1 = {
        compType: "input",
        formData: {
          formName: "显示模式输入框1",
          displayMode: "inline",
          showLabel: true,
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("显示模式输入框1")).toBeInTheDocument();

      // 测试不同显示模式的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "显示模式输入框2",
          displayMode: "block",
          showLabel: false,
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("显示模式输入框2")).toBeInTheDocument();
    });

    it("应该处理组件的不同权限控制", () => {
      // 测试组件的不同权限控制
      const item1 = {
        compType: "input",
        formData: {
          formName: "权限控制输入框1",
          required: true,
          editable: true,
          visible: true,
        },
      };

      render(<RenderItem item={item1} k={1} rule={[]} />);
      expect(screen.getByText("权限控制输入框1")).toBeInTheDocument();

      // 测试不同权限控制的组件
      const item2 = {
        compType: "input",
        formData: {
          formName: "权限控制输入框2",
          required: false,
          editable: false,
          visible: false,
        },
      };

      render(<RenderItem item={item2} k={1} rule={[]} />);
      expect(screen.getByText("权限控制输入框2")).toBeInTheDocument();
    });
  });
});
