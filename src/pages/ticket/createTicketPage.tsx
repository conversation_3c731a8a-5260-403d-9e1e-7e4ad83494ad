import { Banner, Form, TextArea, Toast, useFormState } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { createJobSlice, getJobSlice, updateFormTemplate } from "api";
import {
  certificatePickerData<PERSON>tom,
  contractorEmployeeCertificateFilterAtom,
  jobCertificatesReferValues,
  referJsAtom,
  safetyAnalysisReferValues,
} from "atoms";
import { ReferJsTableModal } from "components";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { convertForm } from "utils/formConverter";
import { SpecialWorkRoutes } from "utils/routerConstants";
import {
  AnalysisTable,
  BaseTicket,
  InfoTicket,
  JobWorkersTable,
  ProcessesTicket,
} from "./content";
import { compareVersions, IsUpdate } from "./utils/compareVersions";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export function CreateTicketPage() {
  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();
  const params = useParams<{ id: string; cid: string }>();
  const reset = useResetAtom(referJsAtom);
  const reset1 = useResetAtom(safetyAnalysisReferValues);
  const reset2 = useResetAtom(jobCertificatesReferValues);
  const reset3 = useResetAtom(certificatePickerDataAtom);
  const reset4 = useResetAtom(contractorEmployeeCertificateFilterAtom);
  const [contractorCertificateFilter, setCertificateFilter] = useAtom(
    contractorEmployeeCertificateFilterAtom
  );
  const [updateItem, setUpdateItem] = useState<IsUpdate>();
  const { data } = useQuery({
    queryKey: ["getJobSlice"],
    queryFn: getJobSlice,
  });

  const mutation = useMutation({
    mutationFn: createJobSlice,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        navigate(-1);
      }
    },
  });

  const update = useMutation({
    mutationFn: updateFormTemplate,
    onSuccess: async (res) => {
      if (res?.code === 0 && res?.data?.id) {
        window.location.replace(
          `${SpecialWorkRoutes.JOB_TMPL}/${params?.cid}/${res?.data?.id}`
        );
      } else {
        setUpdateItem({
          ...(updateItem as any),
          msg: "升级失败,请联系管理员",
        });
      }
    },
    onError: (e) => {
      setUpdateItem({
        ...(updateItem as any),
        msg: "升级失败,请联系管理员",
      });
    },
  });

  useEffect(() => {
    const list = data?.data ?? [];
    const tmpl = list.filter(
      (o) => o?.category?.id === parseInt(params?.cid ?? "1", 10)
    );
    if (tmpl?.length) {
      const res = compareVersions(tmpl[0], true) as IsUpdate;
      if (res?.version && res?.msg) {
        setUpdateItem(res);
        setTimeout(() => {
          // ApiTmpl
          const tmplStr = compareVersions(tmpl[0]);
          update.mutate({
            id: tmpl[0].templates[0].id,
            values: {
              formTemplate: JSON.stringify(tmplStr),
            },
          });
        }, 3000);
      }
    }
  }, [data?.data]);

  const tmplItem: [] = useMemo(() => {
    const list = data?.data ?? [];
    const tmpl = list.filter(
      (o) => o?.category?.id === parseInt(params?.cid ?? "1", 10)
    );

    if (tmpl?.length) {
      return tmpl[0];
    }
    return {};
  }, [data?.data, params?.cid]);

  useEffect(() => {
    setCertificateFilter({
      ...contractorCertificateFilter,
      filter: {
        isBlackFixLogic: 2,
      },
    });
    return () => {
      reset();
      reset1();
      reset2();
      reset3();
      reset4();
    };
  }, []);

  const tmpl = useMemo(() => {
    const tmpl = tmplItem?.templates?.filter(
      (o) => o?.formTemplateId === parseInt(params?.id ?? "1", 10)
    );
    return tmpl?.[0];
  }, [tmplItem, params?.id]);

  // 使用导入的 convertForm 工具函数
  const handleConvertForm = useCallback(
    (form: any) => {
      if (!tmpl?.formTemplate) {
        return [];
      }
      return convertForm(form, JSON.parse(tmpl.formTemplate));
    },
    [tmpl?.formTemplate]
  );

  const handleSubmit = useCallback(
    (values: any) => {
      if (mutation.isLoading) return;

      let from = null;
      try {
        const now = new Date();
        const beginTime = new Date(values?.planBeginTime);
        const endTime = new Date(values?.planEndTime);

        if (endTime <= beginTime || endTime <= now) {
          getFormApiRef.current.setError(
            "planEndTime",
            "计划结束时间必须大于开始时间, 计划结束时间必须大于当前时间"
          );
          getFormApiRef.current.scrollToField("planEndTime");
          return;
        }

        console.log(values.form, "转换前原始数据");
        from = handleConvertForm(values.form);
        console.debug(from, "转换后数据");

        let obj = {
          ...values,
          categoryId: parseInt(`${params?.cid}`),
          referJsId: values?.referJsId ?? null,
          acceptCandidateIds: JSON.stringify(values?.acceptCandidateIds ?? []),
          form: JSON.stringify(from),
          jobProcessesInfo: values?.jobProcessesInfo?.map?.((o) => {
            o.candidatePersonIds = JSON.stringify(o.candidatePersonIds);
            return o;
          }),
        };
        mutation.mutate(obj);
      } catch (e) {
        console.error(e);
      }
    },
    [mutation, params, tmpl, handleConvertForm]
  );

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const isSpecial = Boolean((tmplItem?.category?.isSpecial ?? 1) == 1);
  const isHighWork = Boolean((tmplItem?.category?.specialType ?? 1) == 4);

  return (
    <div className="flex flex-col gap-4 bg-white shadow p-4 h-fit rounded">
      {updateItem?.version && updateItem?.msg ? (
        <Banner type="danger" description={updateItem?.msg} />
      ) : (
        <Form
          getFormApi={handleSetFormApi}
          onSubmit={(values) => {
            handleSubmit(values);
          }}
          autoScrollToError
          labelPosition="top"
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              <ReferJsTableModal />

              <BaseTicket tmpl={tmpl} />
              <InfoTicket
                tmpl={tmpl}
                isSpecial={isSpecial}
                isHighWork={isHighWork}
              />
              {isSpecial ? (
                <>
                  <AnalysisTable />
                  <JobWorkersTable />
                </>
              ) : null}

              <ProcessesTicket tmpl={tmpl} isSpecial={isSpecial} />
              <div className="w-full py-2 gap-2 flex justify-end mt-4">
                <Link to={"/job_tmpl_list"} className="btn rounded  btn-sm">
                  取消
                </Link>
                <button
                  type="submit"
                  className="btn rounded btn-primary btn-sm"
                >
                  保存
                </button>
              </div>
            </>
          )}
        </Form>
      )}
    </div>
  );
}
