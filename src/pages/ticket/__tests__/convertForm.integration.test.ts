import { describe, expect, it } from "vitest";
import { convertForm } from "../../../utils/formConverter";

describe("convertForm - 实际函数集成测试", () => {
  // ★★★ 重要性：核心功能，必须测试
  it("能正确处理基础表单数据转换", () => {
    const form = {
      "name-field": "张三",
      "dept-field": 1,
    };

    const elements = [
      {
        itemId: "name-field",
        compType: "input",
        compName: "姓名",
        formData: {
          formName: "姓名",
        },
      },
      {
        itemId: "dept-field",
        compType: "selector",
        compName: "部门",
        formData: {
          formName: "部门",
        },
      },
    ];

    const result = convertForm(form, elements);

    expect(result).toHaveLength(2);
    expect(result[0].formData.actualValue).toBe("张三");
    expect(result[1].formData.actualValue).toBe(1);
  });

  // ★★★ 重要性：核心功能，必须测试
  it("能正确处理时间字段（满足业务验证要求）", () => {
    const form = {
      "begin-time": "2050-01-01",
      "end-time": "2050-12-31",
    };

    const elements = [
      {
        itemId: "begin-time",
        compType: "datePicker",
        compName: "计划开始时间",
        formData: {
          formName: "planBeginTime",
        },
      },
      {
        itemId: "end-time",
        compType: "datePicker",
        compName: "计划结束时间",
        formData: {
          formName: "planEndTime",
        },
      },
    ];

    const result = convertForm(form, elements);

    expect(result).toHaveLength(2);
    expect(result[0].formData.actualValue).toBe("2050-01-01");
    expect(result[1].formData.actualValue).toBe("2050-12-31");

    // 验证时间逻辑：结束时间应该大于开始时间
    const beginTime = new Date(result[0].formData.actualValue);
    const endTime = new Date(result[1].formData.actualValue);
    expect(endTime.getTime()).toBeGreaterThan(beginTime.getTime());

    // 验证时间逻辑：结束时间应该大于当前时间
    const now = new Date();
    expect(endTime.getTime()).toBeGreaterThan(now.getTime());
  });

  // ★★ 重要性：重要功能，应该测试
  it("能正确处理表格数据", () => {
    const form = {
      "table-data": [
        { name: "张三", role: 1 },
        { name: "李四", role: 2 },
      ],
    };

    const elements = [
      {
        itemId: "table-data",
        compType: "table",
        compName: "人员列表",
        formData: {
          formName: "人员列表",
        },
        children: [
          {
            itemId: "name",
            compType: "input",
            compName: "姓名",
            formData: { formName: "姓名" },
          },
          {
            itemId: "role",
            compType: "selector",
            compName: "角色",
            formData: { formName: "角色" },
          },
        ],
      },
    ];

    const result = convertForm(form, elements);

    expect(result).toHaveLength(1);
    expect(result[0].compType).toBe("table");
    expect(result[0].formData.actualValue).toHaveLength(2);
    
    // 根据实际的数据结构调整断言
    // 第一行数据
    expect(result[0].formData.actualValue[0]).toHaveLength(2);
    expect(result[0].formData.actualValue[0][0].formData.actualValue).toBe("张三");
    expect(result[0].formData.actualValue[0][1].formData.actualValue).toBe(1);
    
    // 第二行数据
    expect(result[0].formData.actualValue[1]).toHaveLength(2);
    expect(result[0].formData.actualValue[1][0].formData.actualValue).toBe("李四");
    expect(result[0].formData.actualValue[1][1].formData.actualValue).toBe(2);
  });

  // ★★ 重要性：重要功能，应该测试
  it("能正确处理空数据", () => {
    const form = {};
    const elements: any[] = [];

    const result = convertForm(form, elements);

    expect(result).toHaveLength(0);
  });

  // ★★ 重要性：重要功能，应该测试
  it("能正确处理未知字段", () => {
    const form = {
      "unknown-field": "test value",
    };

    const elements: any[] = [];

    const result = convertForm(form, elements);

    // 根据实际行为调整期望：未知字段会被过滤掉
    expect(result).toHaveLength(0);
  });
});
