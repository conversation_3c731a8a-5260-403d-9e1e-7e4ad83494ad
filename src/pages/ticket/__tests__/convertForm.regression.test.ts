import { describe, expect, it } from "vitest";
import { convertForm } from "../../../utils/formConverter";

// 测试数据工厂
const createFormElement = (overrides = {}) => ({
  itemId: "test-item-1",
  compType: "input",
  compName: "测试字段",
  formData: {
    formName: "testField",
  },
  ...overrides,
});

const createTableElement = (overrides = {}) => ({
  itemId: "table-1",
  compType: "table",
  compName: "测试表格",
  formData: {
    formName: "testTable",
  },
  children: [
    {
      children: [
        createFormElement({
          itemId: "table-name-1",
          formData: { formName: "姓名" },
        }),
        createFormElement({
          itemId: "table-age-1",
          compType: "selector",
          formData: { formName: "年龄" },
        }),
      ],
    },
  ],
  ...overrides,
});

describe("convertForm - 回归测试补充", () => {
  describe("边界条件测试", () => {
    it("处理空表单数据", () => {
      const elements = [createFormElement()];
      const form = {};

      const result = convertForm(form, elements);

      expect(result).toEqual([]);
    });

    it("处理空元素配置", () => {
      const elements: any[] = [];
      const form = { "test-item-1": "test value" };

      const result = convertForm(form, elements);

      expect(result).toEqual([]);
    });

    it("处理null和undefined值", () => {
      const elements = [createFormElement()];
      const form = {
        "test-item-1": null,
        "test-item-2": undefined,
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toBeNull();
    });

    it("处理大量数据", () => {
      const elements = Array.from({ length: 100 }, (_, index) =>
        createFormElement({
          itemId: `item-${index}`,
          formData: { formName: `field${index}` },
        })
      );
      const form = elements.reduce(
        (acc, element) => {
          acc[element.itemId] = `value-${element.itemId}`;
          return acc;
        },
        {} as Record<string, string>
      );

      const result = convertForm(form, elements);

      expect(result).toHaveLength(100);
      expect(result[0].formData.actualValue).toBe("value-item-0");
      expect(result[99].formData.actualValue).toBe("value-item-99");
    });
  });

  describe("异常数据处理", () => {
    it("处理JSON解析失败的字符串", () => {
      const elements = [
        createFormElement({
          compType: "selector",
          formData: { formName: "testSelector" },
        }),
      ];
      const form = {
        "test-item-1": "invalid json string",
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      // 根据实际代码逻辑，JSON解析失败时会保持原值
      expect(result[0].formData.actualValue).toBe("invalid json string");
    });

    it("处理嵌套的异常数据", () => {
      const elements = [
        createFormElement({
          compType: "table",
          children: [
            {
              children: [
                createFormElement({
                  itemId: "nested-item",
                  compType: "selector",
                }),
              ],
            },
          ],
        }),
      ];
      const form = {
        "nested-item": "invalid json",
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].compType).toBe("table");
    });
  });

  describe("复杂数据结构测试", () => {
    it("处理嵌套表格数据", () => {
      const elements = [
        createTableElement({
          children: [
            {
              children: [
                createFormElement({
                  itemId: "name-1",
                  formData: { formName: "姓名" },
                }),
                createFormElement({
                  itemId: "age-1",
                  formData: { formName: "年龄" },
                }),
              ],
            },
            {
              children: [
                createFormElement({
                  itemId: "name-2",
                  formData: { formName: "姓名" },
                }),
                createFormElement({
                  itemId: "age-2",
                  formData: { formName: "年龄" },
                }),
              ],
            },
          ],
        }),
      ];
      const form = {
        "name-1": "张三",
        "age-1": "25",
        "name-2": "李四",
        "age-2": "30",
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].compType).toBe("table");
      expect(result[0].children).toHaveLength(2);
    });

    it("处理employeePicker数组数据", () => {
      const elements = [
        createFormElement({
          compType: "employeePicker",
          formData: { formName: "员工" },
        }),
      ];
      const form = {
        "test-item-1": [
          { id: 1, name: "张三" },
          { id: 2, name: "李四" },
        ],
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toEqual([
        { id: 1, name: "张三" },
        { id: 2, name: "李四" },
      ]);
    });

    it("处理employeePicker字符串数据", () => {
      const elements = [
        createFormElement({
          compType: "employeePicker",
          formData: { formName: "员工" },
        }),
      ];
      const form = {
        "test-item-1": JSON.stringify([{ id: 1, name: "张三" }]),
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      // 根据实际代码逻辑，字符串会被解析为数组
      expect(result[0].formData.actualValue).toEqual([[{ id: 1, name: "张三" }]]);
    });
  });

  describe("文件上传组件测试", () => {
    it("处理annexImgPicker新上传数据", () => {
      const elements = [
        createFormElement({
          compType: "annexImgPicker",
          formData: { formName: "图片" },
        }),
      ];
      const form = {
        "test-item-1": [
          {
            response: {
              data: {
                uris: ["/upload/image1.jpg", "/upload/image2.jpg"],
              },
            },
          },
        ],
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      // 根据实际代码逻辑，新上传的数据保持原结构
      expect(result[0].formData.actualValue).toEqual([
        {
          response: {
            data: {
              uris: ["/upload/image1.jpg", "/upload/image2.jpg"],
            },
          },
        },
      ]);
    });

    it("处理annexImgPicker已存在数据", () => {
      const elements = [
        createFormElement({
          compType: "annexImgPicker",
          formData: { formName: "图片" },
        }),
      ];
      const form = {
        "test-item-1": [
          {
            url: "http://localhost:3000/upload/existing.jpg",
          },
        ],
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      // 根据实际代码逻辑，已存在的数据保持原结构
      expect(result[0].formData.actualValue).toEqual([
        {
          url: "http://localhost:3000/upload/existing.jpg",
        },
      ]);
    });

    it("处理annexFilePicker混合数据", () => {
      const elements = [
        createFormElement({
          compType: "annexFilePicker",
          formData: { formName: "文件" },
        }),
      ];
      const form = {
        "test-item-1": [
          {
            response: {
              data: {
                uris: ["/upload/new.pdf"],
              },
            },
          },
          {
            url: "http://localhost:3000/upload/existing.pdf",
          },
        ],
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      // 根据实际代码逻辑，混合数据保持原结构
      expect(result[0].formData.actualValue).toEqual([
        {
          response: {
            data: {
              uris: ["/upload/new.pdf"],
            },
          },
        },
        {
          url: "http://localhost:3000/upload/existing.pdf",
        },
      ]);
    });
  });

  describe("数据一致性测试", () => {
    it("确保转换后的数据结构一致", () => {
      const elements = [
        createFormElement({
          itemId: "field1",
          formData: { formName: "字段1" },
        }),
        createFormElement({
          itemId: "field2",
          formData: { formName: "字段2" },
        }),
      ];
      const form = {
        field1: "value1",
        field2: "value2",
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(2);
      expect(result[0].itemId).toBe("field1");
      expect(result[0].formData.formName).toBe("字段1");
      expect(result[0].formData.actualValue).toBe("value1");
      expect(result[1].itemId).toBe("field2");
      expect(result[1].formData.formName).toBe("字段2");
      expect(result[1].formData.actualValue).toBe("value2");
    });

    it("确保表格数据转换正确", () => {
      const elements = [
        createTableElement({
          children: [
            {
              children: [
                createFormElement({
                  itemId: "name",
                  formData: { formName: "姓名" },
                }),
                createFormElement({
                  itemId: "age",
                  formData: { formName: "年龄" },
                }),
              ],
            },
          ],
        }),
      ];
      const form = {
        name: "张三",
        age: "25",
      };

      const result = convertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].compType).toBe("table");
      expect(result[0].children[0].children[0].formData.actualValue).toBe(
        "张三"
      );
      expect(result[0].children[0].children[1].formData.actualValue).toBe("25");
    });
  });

  describe("性能边界测试", () => {
    it("处理大量嵌套数据", () => {
      const startTime = performance.now();

      // 创建1000个嵌套元素
      const elements = Array.from({ length: 1000 }, (_, index) =>
        createFormElement({
          itemId: `item-${index}`,
          formData: { formName: `field${index}` },
        })
      );
      const form = elements.reduce(
        (acc, element, index) => {
          acc[element.itemId] = `value-${index}`;
          return acc;
        },
        {} as Record<string, string>
      );

      const result = convertForm(form, elements);
      const endTime = performance.now();

      expect(result).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
    });
  });
});
