# 导出功能使用文档

## 概述

导出功能是一个基于 React Context 的通用数据导出解决方案，支持将列表数据导出为多种格式（Excel、CSV 等）。它采用 Provider 模式管理导出状态，通过配置化的方式支持不同的导出需求。

## 功能特性

- ✅ **多格式支持**：支持 Excel、CSV 等多种导出格式（可扩展）
- ✅ **配置化导出**：通过列配置控制导出字段和格式
- ✅ **自动数据获取**：集成 API 调用，自动获取完整数据
- ✅ **数据格式化**：支持自定义数据渲染函数
- ✅ **错误处理**：完整的错误处理和用户反馈
- ✅ **文件名生成**：自动生成有意义的文件名
- ✅ **上下文管理**：基于 React Context 的状态管理

## 架构设计

### 1. 核心组件架构

```
src/components/export/
├── index.ts                    # 统一导出入口
├── types.ts                   # 类型定义
├── exportProvider.tsx         # Context Provider
├── useExport.ts              # Hook
├── defaultExportFunction.ts   # 导出函数映射
└── exportToExcel.ts          # Excel 导出实现
```

### 2. 数据流图

```
用户触发导出
    ↓
useExport Hook
    ↓
ExportProvider.exportToFile()
    ↓
API 调用获取数据
    ↓
数据格式化处理
    ↓
调用对应格式的导出函数
    ↓
生成文件并下载
```

### 3. 扩展性设计

```typescript
// 支持的导出格式（可扩展）
export type ExportFormat = "excel" | "csv" | "pdf" | "word";

// 支持的内容类型
export type ExportContentType = "list" | "object";

// 导出函数映射表（可扩展）
export const exportFunctions = {
  excel: { list: exportListToExcel, object: exportObjectToExcel },
  csv: { list: exportListToCSV, object: exportObjectToCSV },
  // ... 更多格式
};
```

## 核心组件详解

### 1. ExportProvider

**作用**：提供导出功能的上下文管理，处理导出逻辑和错误状态。

**核心功能**：

- 管理导出错误状态
- 处理 API 数据获取
- 数据格式化和转换
- 调用具体的导出函数

**关键实现**：

```typescript
export const ExportProvider: React.FC<ExportProviderProps> = ({ children }) => {
  const [exportError, setExportError] = useState<string | null>(null);

  const exportToFile = async (options: ExportOptions) => {
    // 1. 参数验证和默认值设置
    // 2. 获取导出函数
    // 3. API 数据获取
    // 4. 数据格式化处理
    // 5. 调用导出函数
    // 6. 错误处理和用户反馈
  };

  return (
    <ExportContext.Provider value={{ exportToFile, exportError }}>
      {children}
    </ExportContext.Provider>
  );
};
```

### 2. useExport Hook

**作用**：提供访问导出功能的接口。

**特点**：

- 安全的 Context 访问
- 优雅的错误处理
- 可选的导出功能

```typescript
export const useExport = (): ExportContextType | undefined => {
  const context = useContext(ExportContext);
  if (!context) return undefined; // 优雅降级，不抛出错误
  return context;
};
```

### 3. 导出函数系统

**设计模式**：策略模式 + 工厂模式

```typescript
// 导出函数映射表
export const exportFunctions = {
  excel: {
    list: exportListToExcel,
    object: () => console.error("Excel export is not supported for objects."),
  },
  csv: {
    list: () => console.error("CSV export is not supported for list."),
    object: () => console.error("CSV export is not supported for objects."),
  },
  // ... 更多格式
};
```

### 4. Excel 导出实现

**技术栈**：ExcelJS + file-saver

**核心功能**：

- 动态列配置
- 数据格式化
- 样式设置（表头加粗等）
- 智能文件名生成

```typescript
export const exportListToExcel = async (
  data: any[],
  columns: { header: string; field: string }[],
  entityName?: string,
  fileName?: string | null
) => {
  // 1. 创建工作簿和工作表
  // 2. 设置列配置
  // 3. 添加数据行
  // 4. 设置样式
  // 5. 生成文件并下载
};
```

## 使用方式

### 1. 基础集成

首先在应用的根组件或需要导出功能的页面中包装 `ExportProvider`：

```typescript
import { ExportProvider } from "components/export";

// 在页面组件中包装
export const YourPage = () => {
  return (
    <ExportProvider>
      <YourContent />
    </ExportProvider>
  );
};

// 或在具体的详情组件中包装
export const SideDetail = ({ children, ...props }) => {
  return (
    <ExportProvider>
      <div className="side-detail">
        {children}
        <ExportToolbar {...exportConfig} />
      </div>
    </ExportProvider>
  );
};
```

### 2. 基础导出用法

```typescript
import { useExport } from "components/export";

export const ExportButton = () => {
  const exportContext = useExport();

  const handleExport = async () => {
    if (!exportContext) {
      console.warn("导出功能未启用");
      return;
    }

    await exportContext.exportToFile({
      format: "excel",           // 导出格式
      contentType: "list",       // 内容类型
      apiFn: getDataList,        // API 函数
      params: { pageSize: 9999 }, // API 参数
      columns: [                 // 列配置
        { header: "姓名", field: "name" },
        { header: "邮箱", field: "email" },
      ],
      entityName: "用户列表",     // 实体名称
      fileName: "users"          // 文件名（可选）
    });
  };

  return (
    <Button onClick={handleExport}>
      导出 Excel
    </Button>
  );
};
```

### 3. 高级配置用法

```typescript
import { useExport, Column } from "components/export";
import { getTrainingPlanList } from "api";

export const TrainingPlanExportButton = () => {
  const exportContext = useExport();

  // 定义列配置，包含数据格式化
  const exportColumns: Column[] = [
    { header: "计划名称", field: "name" },
    { header: "开始时间", field: "startTime" },
    {
      header: "状态",
      field: "status",
      renderText: (value, item) => {
        // 自定义数据格式化
        const statusMap = { 1: "进行中", 2: "已完成", 3: "已取消" };
        return statusMap[value] || "未知";
      }
    },
    {
      header: "参与人数",
      field: "participantCount",
      renderText: (value) => `${value || 0}人`
    }
  ];

  const handleExport = async () => {
    if (!exportContext) return;

    await exportContext.exportToFile({
      format: "excel",
      contentType: "list",
      apiFn: getTrainingPlanList,
      params: {
        // 传递当前的过滤参数
        ...currentFilters,
        pageSize: 9999,
        pageNumber: 1
      },
      columns: exportColumns,
      entityName: "培训计划",
      fileName: `training_plan_${dayjs().format('YYYY-MM-DD')}`
    });
  };

  return (
    <Button onClick={handleExport} type="primary">
      导出培训计划
    </Button>
  );
};
```

### 4. 结合详情页面的用法

```typescript
// 在详情页面的 TabPane 配置中
export const planPage = () => {
  const exportConfig = {
    apiFn: getTrainingPlanPeopleList,
    entityName: "培训人员",
    columns: [
      { header: "姓名", field: "personName" },
      { header: "部门", field: "personUnit" },
      {
        header: "状态",
        field: "status",
        renderText: (value) => TRAINING_RECORD_STATUS_OPTIONS.find(
          opt => opt.value === value
        )?.label || value
      }
    ]
  };

  const tabsConfig = [
    {
      tab: "培训人员",
      infoOrList: 2, // 表格模式
      enableExport: true,
      exportConfig,
      // ... 其他配置
    }
  ];

  return (
    <ExportProvider>
      <SideDetail tabsConfig={tabsConfig} />
    </ExportProvider>
  );
};
```

## 类型定义

### 核心类型

```typescript
// 导出格式类型
export type ExportFormat = "excel" | "csv";

// 导出内容类型
export type ExportContentType = "list" | "object";

// 列配置类型
export type Column = {
  header: string; // 表头显示文字
  field: string; // 数据字段名称
  renderText?: (value: any, item: any) => any; // 数据格式化函数
};

// 导出选项类型
export type ExportOptions = {
  format: ExportFormat; // 导出格式
  contentType: ExportContentType; // 内容类型
  apiFn: (params: object) => Promise<any>; // API 函数
  params?: object; // API 参数
  columns: Column[]; // 列配置
  entityName?: string; // 实体名称
  fileName?: string; // 文件名
};

// 上下文类型
export interface ExportContextType {
  exportToFile: (options: ExportOptions) => Promise<void>;
  exportError: string | null;
}
```

## 数据处理流程

### 1. API 数据获取

```typescript
// 自动处理分页参数，获取完整数据
let realParams = {
  ...params,
  pageSize: listPageSizeWithoutPaging, // 获取所有数据
  pageNumber: params?.pageNumber || 1,
};
const apiRes = await apiFn(realParams);
```

### 2. 数据格式化处理

```typescript
// 处理列配置和数据格式化
columns.forEach((column) => {
  // 兼容性处理：header 和 title 互相补充
  if (!column.header) {
    column.header = column.title;
  }
  // 兼容性处理：field 和 dataIndex 互相补充
  if (!column.field) {
    column.field = column.dataIndex;
  }

  // 应用自定义格式化函数
  if (column.renderText) {
    data = data.map((item) => {
      item[column.field] = column?.renderText(item[column.field], item);
      return item;
    });
  }
});
```

### 3. 文件名生成

```typescript
// 智能文件名生成
const generateExcelFileName = (entityName?: string): string => {
  const timestamp = dayjs().format("YYYY-MM-DD");
  const prefix = entityName ? entityName : "导出数据";
  return `${prefix}_${timestamp}.xlsx`;
};

// 使用优先级：自定义文件名 > 生成的文件名
const finalFileName = fileName + ".xlsx" || generateExcelFileName(entityName);
```

## 错误处理

### 1. 导出功能不可用

```typescript
export const SafeExportButton = () => {
  const exportContext = useExport();

  const handleExport = async () => {
    // 安全检查：导出功能是否可用
    if (!exportContext) {
      Toast.warning("当前页面不支持导出功能");
      return;
    }

    // 执行导出
    await exportContext.exportToFile(options);
  };
};
```

### 2. API 调用失败

```typescript
// Provider 内部的错误处理
try {
  const apiRes = await apiFn(realParams);
  if (!apiRes || !apiRes?.data) {
    const errorMsg = "导出数据出错，请检查";
    Toast.error(errorMsg);
    setExportError(errorMsg);
    return;
  }
} catch (error) {
  const errorMsg = "数据下载或导出失败，请重试";
  Toast.error(errorMsg);
  setExportError(errorMsg);
}
```

### 3. 格式不支持

```typescript
const exportFunction = exportFunctions[format]?.[contentType];
if (!exportFunction) {
  const errorMsg = `不支持的导出格式: ${format}`;
  Toast.error(errorMsg);
  setExportError(errorMsg);
  return;
}
```

## 扩展开发

### 1. 添加新的导出格式

```typescript
// 1. 扩展类型定义
export type ExportFormat = "excel" | "csv" | "pdf"; // 新增 pdf

// 2. 实现导出函数
export const exportListToPDF = async (
  data: any[],
  columns: Column[],
  entityName?: string,
  fileName?: string | null
) => {
  // PDF 导出实现
};

// 3. 注册到导出函数映射表
export const exportFunctions = {
  excel: { list: exportListToExcel, object: notSupported },
  csv: { list: exportListToCSV, object: notSupported },
  pdf: { list: exportListToPDF, object: notSupported }, // 新增
};
```

### 2. 添加对象类型导出

```typescript
// 实现对象导出函数
export const exportObjectToExcel = async (
  data: object,
  columns: Column[],
  entityName?: string,
  fileName?: string | null
) => {
  // 对象导出实现
};

// 更新映射表
export const exportFunctions = {
  excel: {
    list: exportListToExcel,
    object: exportObjectToExcel, // 新增对象支持
  },
};
```

### 3. 自定义导出配置

```typescript
// 扩展导出选项
export type ExtendedExportOptions = ExportOptions & {
  template?: string; // 模板配置
  watermark?: string; // 水印设置
  password?: string; // 文件密码
  compression?: boolean; // 压缩选项
};
```

## 最佳实践

### 1. Provider 放置位置

```typescript
// ✅ 推荐：在需要导出功能的页面级别包装
export const ListPage = () => {
  return (
    <ExportProvider>
      <PageContent />
    </ExportProvider>
  );
};

// ❌ 不推荐：在全局 App 级别包装（除非全站都需要）
export const App = () => {
  return (
    <ExportProvider>  {/* 可能造成不必要的 Context 开销 */}
      <Router />
    </ExportProvider>
  );
};
```

### 2. 列配置管理

```typescript
// ✅ 推荐：将列配置提取为常量
const EXPORT_COLUMNS: Column[] = [
  { header: "姓名", field: "name" },
  {
    header: "状态",
    field: "status",
    renderText: (value) => STATUS_MAP[value] || value,
  },
];

// ✅ 推荐：复用表格列配置
const tableColumns = [
  { title: "姓名", dataIndex: "name" },
  { title: "状态", dataIndex: "status" },
];

const exportColumns = tableColumns.map((col) => ({
  header: col.title,
  field: col.dataIndex,
}));
```

### 3. 错误处理策略

```typescript
// ✅ 推荐：完整的错误处理
const handleExport = async () => {
  if (!exportContext) {
    Toast.warning("导出功能暂不可用");
    return;
  }

  try {
    setLoading(true);
    await exportContext.exportToFile(options);
    Toast.success("导出成功");
  } catch (error) {
    Toast.error("导出失败，请重试");
    console.error("Export error:", error);
  } finally {
    setLoading(false);
  }
};
```

### 4. 性能优化

```typescript
// ✅ 推荐：使用 useMemo 缓存列配置
const exportColumns = useMemo(
  () => [
    { header: "姓名", field: "name" },
    {
      header: "状态",
      field: "status",
      renderText: (value) => STATUS_MAP[value] || value,
    },
  ],
  []
);

// ✅ 推荐：使用 useCallback 缓存导出函数
const handleExport = useCallback(async () => {
  if (!exportContext) return;
  await exportContext.exportToFile({
    format: "excel",
    contentType: "list",
    apiFn: getDataList,
    params: filters,
    columns: exportColumns,
    entityName: "数据列表",
  });
}, [exportContext, filters, exportColumns]);
```

## 常见问题

### Q1: 导出功能不生效怎么办？

**A**: 检查是否正确包装了 `ExportProvider`，以及 `useExport` 是否在 Provider 内部调用。

### Q2: 导出的数据格式不正确？

**A**: 检查 `renderText` 函数是否正确实现，以及 `field` 字段是否与数据结构匹配。

### Q3: 如何处理大量数据导出？

**A**: 当前实现会一次性获取所有数据，对于大量数据建议：

- 在服务端实现分页导出
- 添加导出进度提示
- 考虑使用 Web Workers 处理数据

### Q4: 如何自定义文件名？

**A**: 通过 `fileName` 参数指定（不包含扩展名），或通过 `entityName` 影响自动生成的文件名。

### Q5: 导出功能在某些页面不可用？

**A**: 确保在需要导出功能的页面正确包装了 `ExportProvider`，或者优雅处理导出功能不可用的情况。

## 参考示例

完整的导出功能实现可以参考：

- `src/components/detail/ExportToolbar.tsx` - 导出工具栏组件
- `src/pages/coporateTraining/planPage.tsx` - 详情页面导出配置
- `src/components/detail/renderSideTabPane.tsx` - 详情页面集成

## 相关文档

- [ExcelJS 官方文档](https://github.com/exceljs/exceljs)
- [file-saver 使用指南](https://github.com/eligrey/FileSaver.js)
- [React Context 最佳实践](https://react.dev/learn/scaling-up-with-reducer-and-context)
