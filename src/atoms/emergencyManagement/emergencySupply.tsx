import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  EMERGENCYSUPPLY_STATUS_MAP,
  EMERGENCYSUPPLY_TYPE_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const emergencySupplyFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const emergencySupplyFnAtom = atom({
  refetch: () => {},
});

export const emergencySupplyEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const emergencySupplyConfigModalAtom = atom(false);

export const emergencySupplyColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "物资名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "物资编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "物资分类",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EMERGENCYSUPPLY_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "物资状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EMERGENCYSUPPLY_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "物资用途",
    dataIndex: "usage",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "物资型号",
    dataIndex: "model",
    isShow: true,
    ellipsis: true,
  },
]);

export const emergencySupplyAtoms: CommonAtoms = {
  entity: "EmergencySupply",
  filter: emergencySupplyFilterAtom,
  Fn: emergencySupplyFnAtom,
  editModal: emergencySupplyEditModalAtom,
  configModal: emergencySupplyConfigModalAtom,
  columns: emergencySupplyColumnsAtom,
};
