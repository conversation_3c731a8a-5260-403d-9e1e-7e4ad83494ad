import { IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const dangerousProcessFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const dangerousProcessFnAtom = atom({
  refetch: () => {},
});

export const dangerousProcessEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const dangerousProcessConfigModalAtom = atom(false);

export const dangerousProcessColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "工艺名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "工艺编码",
    dataIndex: "code",
    isShow: true,
  },
  {
    title: "是否重点监管化工工艺",
    dataIndex: "isKeyRegulatory",
    isShow: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return <span>{i.name}</span>;
    },
  },
  {
    title: "岗位操作人数",
    dataIndex: "operationPersonNumber",
    isShow: true,
  },
  {
    title: "持证人数",
    dataIndex: "qualifiedPersonNumber",
    isShow: true,
  },
]);

export const dangerousProcessAtoms: CommonAtoms = {
  entity: "DangerousProcess",
  filter: dangerousProcessFilterAtom,
  Fn: dangerousProcessFnAtom,
  editModal: dangerousProcessEditModalAtom,
  configModal: dangerousProcessConfigModalAtom,
  columns: dangerousProcessColumnsAtom,
};
