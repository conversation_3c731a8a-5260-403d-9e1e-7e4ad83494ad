import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { OVERTIME_STATUS_MAP } from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { millisecondsOfOnemonth } from "utils";

export const enterpriseCertificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const enterpriseCertificateFnAtom = atom({
  refetch: () => {},
});

export const enterpriseCertificateEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const enterpriseCertificateConfigModalAtom = atom(false);

export const enterpriseCertificateColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "证书名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "证书编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "证书类型",
    dataIndex: "type",
    isShow: true,
  },
  {
    title: "发证机构",
    dataIndex: "issueAuthority",
    isShow: true,
  },
  {
    title: "到期日期",
    dataIndex: "expireDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
    renderText: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");
      return dateShow;
    },
  },
  {
    title: "是否过期",
    dataIndex: "status",
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(OVERTIME_STATUS_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const enterpriseCertificateAtoms: CommonAtoms = {
  entity: "EnterpriseCertificate",
  entityCName: "企业证书信息",
  filter: enterpriseCertificateFilterAtom,
  Fn: enterpriseCertificateFnAtom,
  editModal: enterpriseCertificateEditModalAtom,
  configModal: enterpriseCertificateConfigModalAtom,
  columns: enterpriseCertificateColumnsAtom,
};
