import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_REPORT_STATUS_MAP,
  EQUIPMENT_STATUS_MAP,
  REPORT_STATUS_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate, millisecondsOfOnemonth } from "utils";

export const equipmentFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  equipmentCategoryId: null,
  filter: {},
});

//TODO: to delete
export const equipmentFnAtom = atom({
  refetch: () => {},
});

export const equipmentEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const equipmentConfigModalAtom = atom(false);

export const equipmentColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "设备名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "设备编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "设备状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_STATUS_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "所属区域",
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="到期日期">到期日期</Tooltip>,
    dataIndex: "expireDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null; // 如果 text 是 null 或 undefined，直接返回 null，不渲染任何内容
      }

      const date = dayjs(text);
      const diff = date.diff(dayjs());

      return (
        <p>
          {diff < millisecondsOfOnemonth ? (
            <Tag color="red">{date.format("YYYY-MM-DD")}</Tag>
          ) : diff < 3 * millisecondsOfOnemonth ? (
            <Tag color="yellow">{date.format("YYYY-MM-DD")}</Tag>
          ) : (
            <Tag color="white">{date.format("YYYY-MM-DD")}</Tag>
          )}
        </p>
      );
    },
  },
  {
    title: "是否上报",
    dataIndex: "needReport",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content || "-"}>
          <p>{content || "-"}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "上报应答",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      return (
        <Tooltip content={item || "-"}>
          <p>{item || "-"}</p>
        </Tooltip>
      );
    },
  },
]);

export const equipmentAtoms: CommonAtoms = {
  entity: "Equipment",
  filter: equipmentFilterAtom,
  Fn: equipmentFnAtom,
  editModal: equipmentEditModalAtom,
  configModal: equipmentConfigModalAtom,
  columns: equipmentColumnsAtom,
};
