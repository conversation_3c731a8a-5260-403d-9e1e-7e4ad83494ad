import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  IS_ISNOT_MAP,
  MONITOR_TYPE_MAP,
  VIDEO_TYPE_MAP,
  VideoModal,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const monitorEditModal = atom({
  id: "",
  show: false,
});

export const monitorConfigModalAtom = atom(false);
// 查询条件
export const monitorFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const monitorFnAtom = atom({
  refetch: () => {},
});

export const monitorColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="摄像头名称">摄像头名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
    ellipsis: true,
    render: (item, record) => {
      return <VideoModal data={record} />;
    },
  },
  {
    title: <Tooltip content="摄像头类型">摄像头类型</Tooltip>,
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(MONITOR_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="监控区域类型">监控区域类型</Tooltip>,
    dataIndex: "videoType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(VIDEO_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "所属区域",
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "是否在一张图中显示",
    dataIndex: "isInMap",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
