import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { BbTaskParams } from "api";
import {
  BB_WORK_TYPE_MAP,
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq, whereEq } from "ramda";

export const bbTaskConfigModalAtom = atom(false);

export const bbTaskEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const bbTaskFilterAtom = atomWithReset<BbTaskParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const bbTaskFnAtom = atom({
  refetch: () => {},
});

export const bbTaskColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
    fixed: true,
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="任务类型">任务类型</Tooltip>,
    dataIndex: "bbWorkType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(BB_WORK_TYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="包保责任对应编号">包保责任对应编号</Tooltip>,
    dataIndex: "bbTaskNum",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "包保责任",
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    // title: <Tooltip content="排查周期">排查周期</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // }, {
    title: <Tooltip content="排查周期">排查周期</Tooltip>,
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit ? record?.checkCycleUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i?.name === "月" ? "个" : ""}
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="持续时间">持续时间</Tooltip>,
    dataIndex: "duration",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.durationUnit ? record?.durationUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.duration === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.duration}
          {i?.name === "月" ? "个" : ""}
          {i?.name}
        </Tag>
      );
    },
  },
]);
