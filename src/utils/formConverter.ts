import { base_url } from "config";
import { find, isEmpty, propEq } from "ramda";

/**
 * 表单数据转换工具函数
 * 将表单原始数据转换为业务所需的数据结构
 *
 * @param form - 表单原始数据
 * @param elements - 表单元素配置数组
 * @returns 转换后的数据数组
 */
export const convertForm = (form: any, elements: any[] = []): any[] => {
  const results: any[] = [];

  Object.keys(form).forEach((key) => {
    let value = form[key];

    const business = find(propEq(key, "business"))(elements) as any;
    const itemId = find(propEq(key, "itemId"))(elements) as any;
    const item: any = business ? business : itemId;

    // 处理 employeePicker 和 selector 类型
    if (
      item &&
      (item.compType === "employeePicker" || item.compType === "selector")
    ) {
      if (Array.isArray(value)) {
        value = value.map((o) => (typeof o === "object" ? o : JSON.parse(o)));
      } else if (typeof value === "string") {
        try {
          value = [JSON.parse(value)];
        } catch (e) {
          console.log(e, "=== JSON.parse(value)");
          // 保持原值
        }
      }
    }

    // 处理 annexImgPicker 和 annexFilePicker 类型
    if (
      business &&
      (business.compType === "annexImgPicker" ||
        business.compType === "annexFilePicker")
    ) {
      if (Array.isArray(value)) {
        value = value
          .map((o) => {
            if (o?.response) {
              // 新上传的数据
              return (o?.response?.data?.uris ?? []).map((u) => u);
            } else {
              const rawData = o?.url?.split(base_url);
              return rawData?.length ? rawData[1] : null;
            }
          })
          .flat();
      }
    }

    // 处理表格类型
    if (item && item.compType === "table") {
      value = value.map((row: any) => convertForm(row, item.children));
    }

    if (item) {
      results.push({
        ...item,
        formData: {
          ...item.formData,
          actualValue: value,
        },
      });
    } else {
      // 占位
      results.push(null);
    }
  });

  let oriTableData = elements.filter((o) => o?.compType === "table")?.[0];
  let newTableData;
  let newTable_child: any[] = [];

  if (!isEmpty(oriTableData)) {
    let values: Record<any, any> = {};
    // 获取表格内数据
    Object.keys(form).forEach((key) => {
      elements.forEach((e: any) => {
        if (e.compType === "table") {
          (e?.children ?? []).forEach((cell: any) => {
            (cell?.children ?? []).forEach((comp: any) => {
              if (comp.itemId === key) {
                values[`${key}`] = form[key];
              } else if (comp?.formData?.actualValue) {
                values[`${comp.itemId}`] = comp?.formData?.actualValue;
              }
            });
          });
        }
      });
    });

    (oriTableData?.children ?? []).forEach((cell: any) => {
      const cell_child = [];
      (cell?.children ?? []).forEach((comp: any) => {
        const tmp = {
          ...comp,
          formData: {
            ...comp.formData,
            actualValue: values[comp.itemId],
          },
        };
        cell_child.push(tmp);
      });
      newTable_child.push({
        ...cell,
        children: cell_child,
      });
    });
    newTableData = {
      ...oriTableData,
      children: newTable_child,
    };
  }

  // 插入 newTableData 到 results 的第一个 null 位置
  const firstNullIndex = results.indexOf(null);
  if (firstNullIndex !== -1) {
    results[firstNullIndex] = newTableData;
  }

  // 过滤掉 results 中的其他 null 值
  const filteredResults = results.filter(
    (item) => item !== null && item?.itemId
  );

  return filteredResults;
};
