import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vitest/config";

export default defineConfig({
  plugins: [react()],
  test: {
    environment: "jsdom",
    setupFiles: ["./src/test/setup.ts"],
    globals: true,
    css: true,
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html", "lcov"],
      exclude: [
        "node_modules/",
        "src/test/",
        "**/*.d.ts",
        "**/*.config.*",
        "**/coverage/**",
        "**/dist/**",
        "**/build/**",
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 85,
          lines: 80,
          statements: 80,
        },
        "./src/pages/ticket/": {
          branches: 80,
          functions: 90,
          lines: 85,
          statements: 85,
        },
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@api": path.resolve(__dirname, "./src/api"),
      "@atoms": path.resolve(__dirname, "./src/atoms"),
      "@components": path.resolve(__dirname, "./src/components"),
      "@types": path.resolve(__dirname, "./src/types"),
      "@utils": path.resolve(__dirname, "./src/utils"),
      "@pages": path.resolve(__dirname, "./src/pages"),
      "api": path.resolve(__dirname, "./src/api"),
      "atoms": path.resolve(__dirname, "./src/atoms"),
      "components": path.resolve(__dirname, "./src/components"),
      "hooks": path.resolve(__dirname, "./src/hooks"),
      "utils": path.resolve(__dirname, "./src/utils"),
      "types": path.resolve(__dirname, "./src/types"),
      "config": path.resolve(__dirname, "./src/config"),
      "mocks": path.resolve(__dirname, "./src/mocks"),
    },
  },
});
 